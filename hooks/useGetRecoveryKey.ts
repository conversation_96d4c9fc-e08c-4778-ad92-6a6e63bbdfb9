import { useQuery } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { queryKeys } from '@/utils/queryKeys';

export const RECOVERY_KEY_STORE_KEY = 'geoSafeRecoveryKey';

export const useGetRecoveryKey = () => {
  return useQuery({
    queryKey: queryKeys.recoveryKey,
    queryFn: async () => {
      return await SecureStore.getItemAsync(RECOVERY_KEY_STORE_KEY);
    },
  });
};
