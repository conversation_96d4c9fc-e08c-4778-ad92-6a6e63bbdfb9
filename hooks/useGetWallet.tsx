import { useQuery } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { decodeWallet } from '@/utils/genWallet';
import { queryKeys } from '@/utils/queryKeys';
import { useGetRecoveryKey } from './useGetRecoveryKey';
import { PRIVATE_KEY_STORE_KEY, SEED_PHRASE_STORE_KEY } from './useSetupWallet';

export const useGetWallet = () => {
  const { data: recoveryKey } = useGetRecoveryKey();

  return useQuery({
    queryKey: queryKeys.wallet,
    queryFn: async () => {
      if (!recoveryKey) return null;

      const [privateKeyHash, seedPhraseHash] = await Promise.all([
        SecureStore.getItemAsync(PRIVATE_KEY_STORE_KEY),
        SecureStore.getItemAsync(SEED_PHRASE_STORE_KEY),
      ]);
      if (!privateKeyHash || !seedPhraseHash) return null;

      const { privateKey, seedPhrase } = decodeWallet(recoveryKey, privateKeyHash, seedPhraseHash);
      return { privateKey, seedPhrase };
    },
  });
};
